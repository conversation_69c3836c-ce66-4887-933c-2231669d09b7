import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Circle, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { MapPin } from 'lucide-react';
import { useFilterStore } from '../store/filterStore';
import { Place } from '../types/place';

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// Custom icons for different place types
const createCustomIcon = (type: 'restaurant' | 'grocery' | 'user') => {
  const iconHtml = type === 'restaurant'
    ? '<div style="background: #DD6B20; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M7 21h10"></path><path d="M12 21a9 9 0 0 0 9-9H3a9 9 0 0 0 9 9Z"></path></svg></div>'
    : type === 'grocery'
    ? '<div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path><path d="M3 6h18"></path><path d="M16 10a4 4 0 0 1-8 0"></path></svg></div>'
    : '<div style="background: #2563EB; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg></div>';

  return L.divIcon({
    html: iconHtml,
    className: 'custom-div-icon',
    iconSize: [30, 30],
    iconAnchor: [15, 15],
  });
};

interface MapViewProps {
  places?: Place[];
  height?: string;
  interactive?: boolean;
  singlePlace?: Place;
  className?: string;
}

const MapView: React.FC<MapViewProps> = ({
  places,
  height = 'h-[300px] sm:h-[400px]',
  interactive = true,
  singlePlace,
  className = ''
}) => {
  const { userLocation, setUserLocation, distance } = useFilterStore();
  const defaultLocation = { lat: 40.7128, lng: -74.0060 }; // New York as default

  // Get user location on mount
  useEffect(() => {
    if (!singlePlace && interactive) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLoc = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserLocation(userLoc);
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  }, [singlePlace, interactive, setUserLocation]);

  // Determine map center and zoom
  const getMapCenter = (): [number, number] => {
    if (singlePlace) {
      return [singlePlace.location.lat, singlePlace.location.lng];
    }
    if (userLocation) {
      return [userLocation.lat, userLocation.lng];
    }
    return [defaultLocation.lat, defaultLocation.lng];
  };

  const getMapZoom = (): number => {
    return singlePlace ? 14 : 11;
  };

  return (
    <div className={`rounded-lg overflow-hidden relative ${height} ${className}`}>
      {!interactive && !singlePlace && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 z-10 text-white">
          <MapPin className="w-6 h-6 mr-2" />
          <span className="font-medium">Login to view the map</span>
        </div>
      )}

      <MapContainer
        center={getMapCenter()}
        zoom={getMapZoom()}
        className="h-full w-full"
        zoomControl={true}
        scrollWheelZoom={interactive}
        dragging={interactive}
        touchZoom={interactive}
        doubleClickZoom={interactive}
        boxZoom={interactive}
        keyboard={interactive}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* User location marker */}
        {userLocation && !singlePlace && (
          <>
            <Marker
              position={[userLocation.lat, userLocation.lng]}
              icon={createCustomIcon('user')}
            >
              <Popup>Your Location</Popup>
            </Marker>

            {/* Distance circle */}
            {distance && (
              <Circle
                center={[userLocation.lat, userLocation.lng]}
                radius={distance * 1000} // Convert km to meters
                pathOptions={{
                  color: '#2F855A',
                  fillColor: '#2F855A',
                  fillOpacity: 0.1,
                  weight: 2,
                }}
              />
            )}
          </>
        )}

        {/* Place markers */}
        {places && places.map((place) => (
          <Marker
            key={place.id}
            position={[place.location.lat, place.location.lng]}
            icon={createCustomIcon(place.type)}
            eventHandlers={{
              click: () => {
                window.location.href = `/place/${place.id}`;
              },
            }}
          >
            <Popup>
              <div className="font-medium">{place.name}</div>
              <div className="text-sm text-gray-500">
                {place.type === 'restaurant' ? 'Restaurant' : 'Grocery'}
              </div>
              {place.cuisine_type && (
                <div className="text-sm text-gray-400">{place.cuisine_type}</div>
              )}
            </Popup>
          </Marker>
        ))}

        {/* Single place marker */}
        {singlePlace && (
          <Marker
            position={[singlePlace.location.lat, singlePlace.location.lng]}
            icon={createCustomIcon(singlePlace.type)}
          >
            <Popup>
              <div className="font-medium">{singlePlace.name}</div>
              <div className="text-sm text-gray-500">
                {singlePlace.type === 'restaurant' ? 'Restaurant' : 'Grocery'}
              </div>
              {singlePlace.cuisine_type && (
                <div className="text-sm text-gray-400">{singlePlace.cuisine_type}</div>
              )}
            </Popup>
          </Marker>
        )}
      </MapContainer>
    </div>
  );
};

export default MapView;