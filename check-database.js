// Simple script to check if Supabase database has sample data
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ltxqqcvqiyheaeiszfgg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHFxY3ZxaXloZWFlaXN6ZmdnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNzI4NDMsImV4cCI6MjA2NDY0ODg0M30.kbm1iTcTr6fKH85CWYUNqTam4yFMoMldIjge4IRPlhM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  try {
    console.log('🔍 Checking Supabase database...');
    
    const { data, error } = await supabase
      .from('places')
      .select('*')
      .limit(5);

    if (error) {
      console.error('❌ Error fetching data:', error.message);
      console.log('\n💡 This might mean:');
      console.log('   1. The places table doesn\'t exist yet');
      console.log('   2. You need to run the Supabase migrations');
      console.log('   3. There\'s a connection issue');
      return;
    }

    if (!data || data.length === 0) {
      console.log('📭 Database is empty - no places found');
      console.log('\n💡 You need to run the migrations to add sample data:');
      console.log('   1. Go to your Supabase dashboard');
      console.log('   2. Run the SQL migrations in the supabase/migrations folder');
      console.log('   3. Or manually insert some test data');
    } else {
      console.log(`✅ Found ${data.length} places in database:`);
      data.forEach((place, index) => {
        console.log(`   ${index + 1}. ${place.name} (${place.type})`);
        if (place.image_url) {
          console.log(`      Image: ${place.image_url.substring(0, 50)}...`);
        } else {
          console.log('      No image URL');
        }
      });
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

checkDatabase();
