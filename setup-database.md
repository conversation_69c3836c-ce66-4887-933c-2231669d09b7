# 🗄️ Database Setup Instructions

## Quick Setup (Copy & Paste Method)

### Step 1: Create Tables and Security
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Click **SQL Editor** → **New Query**
4. Copy and paste this SQL:

```sql
-- <PERSON><PERSON> places table
CREATE TABLE IF NOT EXISTS places (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('restaurant', 'grocery')),
  location JSONB NOT NULL,
  address TEXT NOT NULL,
  cuisine_type TEXT,
  dietary_tags TEXT[] DEFAULT '{}' NOT NULL,
  religion_tags TEXT[] DEFAULT '{}' NOT NULL,
  price_level SMALLINT NOT NULL CHECK (price_level BETWEEN 1 AND 3),
  description TEXT,
  image_url TEXT
);

-- Create profiles table that connects to auth.users
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  dietary_preferences TEXT[] DEFAULT '{}' NOT NULL,
  religious_preferences TEXT[] DEFAULT '{}' NOT NULL,
  favorite_places INTEGER[] DEFAULT '{}' NOT NULL
);

-- Enable Row Level Security
ALTER TABLE places ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for places table
CREATE POLICY "Anyone can read places"
  ON places
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create policies for profiles table
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Create a trigger to create a profile when a new user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id)
  VALUES (new.id);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER create_profile_for_user
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

5. Click **Run** button

### Step 2: Add Sample Data
1. Create another **New Query**
2. Copy and paste this SQL:

```sql
-- Sample restaurants and grocery stores
INSERT INTO places (name, type, location, address, cuisine_type, dietary_tags, religion_tags, price_level, description, image_url)
VALUES
  (
    'Green Garden Bistro', 
    'restaurant',
    '{"lat": 40.7128, "lng": -74.0060}',
    '123 Broadway, New York, NY 10007',
    'Vegetarian',
    ARRAY['vegan', 'vegetarian', 'gluten-free'],
    ARRAY['halal', 'kosher'],
    2,
    'A plant-based paradise offering creative, seasonal dishes in a warm, eco-friendly atmosphere.',
    'https://images.pexels.com/photos/260922/pexels-photo-260922.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Halal Grill House', 
    'restaurant',
    '{"lat": 40.7282, "lng": -73.9882}',
    '456 5th Avenue, New York, NY 10016',
    'Mediterranean',
    ARRAY['carnivore', 'paleo'],
    ARRAY['halal'],
    2,
    'Authentic Mediterranean cuisine with halal-certified meats, offering shawarma, kebabs, and falafel.',
    'https://images.pexels.com/photos/5490876/pexels-photo-5490876.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Kosher Delight', 
    'restaurant',
    '{"lat": 40.7589, "lng": -73.9851}',
    '789 Madison Ave, New York, NY 10065',
    'Deli',
    ARRAY['carnivore'],
    ARRAY['kosher'],
    2,
    'Traditional kosher delicatessen with house-made pastrami, matzo ball soup, and fresh-baked breads.',
    'https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Keto Kitchen', 
    'restaurant',
    '{"lat": 40.7112, "lng": -74.0123}',
    '101 Hudson St, New York, NY 10013',
    'American',
    ARRAY['keto', 'carnivore', 'gluten-free'],
    ARRAY['lent-friendly'],
    3,
    'Specializing in low-carb, high-fat meals for keto lifestyle enthusiasts without compromising on flavor.',
    'https://images.pexels.com/photos/67468/pexels-photo-67468.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Pure Vegan Cafe', 
    'restaurant',
    '{"lat": 40.7234, "lng": -73.9875}',
    '222 E 14th St, New York, NY 10003',
    'Vegan',
    ARRAY['vegan', 'gluten-free', 'dairy-free'],
    ARRAY['halal', 'kosher'],
    1,
    'Cozy cafe with 100% plant-based menu featuring global comfort foods and decadent desserts.',
    'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Lenten Fish Grill', 
    'restaurant',
    '{"lat": 40.7412, "lng": -73.9974}',
    '333 W 23rd St, New York, NY 10011',
    'Seafood',
    ARRAY['gluten-free', 'dairy-free'],
    ARRAY['lent-friendly'],
    3,
    'Specializing in seafood dishes perfect for the Lenten season, with sustainably-sourced fish and shellfish.',
    'https://images.pexels.com/photos/262959/pexels-photo-262959.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Organic Market', 
    'grocery',
    '{"lat": 40.7354, "lng": -73.9912}',
    '444 Park Ave, New York, NY 10016',
    NULL,
    ARRAY['vegan', 'vegetarian', 'gluten-free', 'organic'],
    ARRAY['halal', 'kosher'],
    2,
    'Full-service organic grocery store with extensive vegan and gluten-free options.',
    'https://images.pexels.com/photos/2733918/pexels-photo-2733918.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Halal Meats & More', 
    'grocery',
    '{"lat": 40.7483, "lng": -73.9729}',
    '555 Lexington Ave, New York, NY 10022',
    NULL,
    ARRAY['carnivore'],
    ARRAY['halal'],
    1,
    'Specializing in halal-certified meats, poultry, and imported Middle Eastern groceries.',
    'https://images.pexels.com/photos/1036857/pexels-photo-1036857.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Kosher Pantry', 
    'grocery',
    '{"lat": 40.7731, "lng": -73.9822}',
    '666 3rd Ave, New York, NY 10017',
    NULL,
    ARRAY['gluten-free', 'vegetarian'],
    ARRAY['kosher'],
    2,
    'Your one-stop shop for all kosher needs, from everyday essentials to holiday specialties.',
    'https://images.pexels.com/photos/1132558/pexels-photo-1132558.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Low Carb Essentials', 
    'grocery',
    '{"lat": 40.7168, "lng": -74.0465}',
    '777 Washington St, New York, NY 10014',
    NULL,
    ARRAY['keto', 'paleo', 'carnivore'],
    ARRAY[],
    3,
    'Specialized grocery store featuring low-carb and keto-friendly foods, supplements, and snacks.',
    'https://images.pexels.com/photos/264636/pexels-photo-264636.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Vegan Valley', 
    'grocery',
    '{"lat": 40.7281, "lng": -73.9467}',
    '888 Bedford Ave, Brooklyn, NY 11205',
    NULL,
    ARRAY['vegan', 'vegetarian', 'dairy-free'],
    ARRAY['halal', 'kosher', 'lent-friendly'],
    1,
    'Plant-based grocery store offering a wide range of vegan alternatives, produce, and bulk foods.',
    'https://images.pexels.com/photos/1392585/pexels-photo-1392585.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Faith Foods', 
    'grocery',
    '{"lat": 40.7489, "lng": -73.9680}',
    '999 2nd Ave, New York, NY 10017',
    NULL,
    ARRAY['vegetarian', 'gluten-free'],
    ARRAY['halal', 'kosher', 'lent-friendly'],
    2,
    'Grocery store specializing in foods that meet various religious dietary requirements.',
    'https://images.pexels.com/photos/3962294/pexels-photo-3962294.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  );
```

3. Click **Run** button

## ✅ Verification

After running both queries, you should see:
- 12 places added to your database
- All with proper image URLs from Pexels
- Mix of restaurants and grocery stores
- Various dietary and religious options

Your app should now display the map with markers and restaurant cards with images!
